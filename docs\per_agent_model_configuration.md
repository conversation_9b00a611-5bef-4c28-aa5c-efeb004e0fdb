# Per-Agent Model Configuration Guide

This guide explains how to configure different language models for individual agents in the agent framework, leveraging Microsoft AutoGen's capabilities and design patterns.

## Overview

The enhanced agent framework now supports per-agent model configuration, allowing you to:

- **Use different model providers** for different agents (OpenAI, Anthropic, Azure, local models)
- **Optimize model selection** based on agent capabilities and tasks
- **Implement fallback mechanisms** for robust operation
- **Leverage AutoGen's tools and workbench patterns**
- **Maintain backward compatibility** with existing configurations

## Supported Model Providers

### OpenAI
- **Models**: GPT-4, GPT-4o, GPT-3.5-turbo, etc.
- **Use cases**: Code analysis, complex reasoning tasks
- **Configuration**: Requires OpenAI API key

### Anthropic Claude
- **Models**: Claude-3.5-sonnet, Claude-3-haiku, etc.
- **Use cases**: Documentation, creative writing, research
- **Configuration**: Requires Anthropic API key

### Azure OpenAI
- **Models**: Azure-hosted OpenAI models
- **Use cases**: Enterprise deployments with Azure integration
- **Configuration**: Requires Azure endpoint and deployment details

### Local Models (Ollama)
- **Models**: CodeLlama, Llama2, custom models
- **Use cases**: Privacy-sensitive tasks, offline operation
- **Configuration**: Requires local Ollama installation

### OpenRouter
- **Models**: Access to multiple providers through one API
- **Use cases**: Model experimentation, cost optimization
- **Configuration**: Requires OpenRouter API key

## Basic Configuration

### Simple Per-Agent Configuration

```python
from agent_framework.core.config import ModelConfig, ModelProvider, AgentRoleConfig

# OpenAI configuration for code analysis
openai_config = ModelConfig(
    provider=ModelProvider.OPENAI,
    model="gpt-4o",
    api_key="your-openai-key",
    temperature=0.3,
    max_tokens=8192
)

# Anthropic configuration for documentation
anthropic_config = ModelConfig(
    provider=ModelProvider.ANTHROPIC,
    model="claude-3-5-sonnet-20241022",
    api_key="your-anthropic-key",
    temperature=0.8,
    max_tokens=4096
)

# Agent role configurations
code_analyst = AgentRoleConfig(
    name="code_analyst",
    description="Expert code analyzer using GPT-4",
    system_message="You are a code analysis expert...",
    capabilities=["code_analysis", "error_detection"],
    model_config=openai_config  # Uses OpenAI
)

documenter = AgentRoleConfig(
    name="documenter",
    description="Documentation specialist using Claude",
    system_message="You are a documentation expert...",
    capabilities=["documentation"],
    model_config=anthropic_config  # Uses Anthropic
)
```

### Advanced Configuration with Fallbacks

```python
# Primary configuration with fallbacks
primary_config = ModelConfig(
    provider=ModelProvider.OPENAI,
    model="gpt-4o",
    api_key="your-openai-key",
    fallback_configs=[
        ModelConfig(
            provider=ModelProvider.ANTHROPIC,
            model="claude-3-5-sonnet-20241022",
            api_key="your-anthropic-key"
        ),
        ModelConfig(
            provider=ModelProvider.OPENROUTER,
            model="qwen/qwen3-coder:free",
            api_key="your-openrouter-key"
        )
    ]
)
```

## AutoGen Integration

### Using AutoGen Tools

```python
from agent_framework.core.autogen_integration import autogen_tools_manager

# Initialize AutoGen tools
await autogen_tools_manager.initialize()

# Register custom function tools
def calculate_complexity(code: str) -> float:
    """Calculate code complexity score."""
    # Implementation here
    return 0.5

complexity_tool = autogen_tools_manager.register_function_tool(
    name="code_complexity",
    func=calculate_complexity,
    description="Calculate code complexity metrics"
)

# Use tools in agent configuration
agent_config = AgentRoleConfig(
    name="analyzer",
    model_config=openai_config,
    # Tools will be automatically integrated
)
```

### Workbench Patterns

```python
from agent_framework.core.autogen_integration import autogen_workbench

# Create agents with different models
await autogen_workbench.create_agent_with_model(
    agent_name="analyst",
    model_config=openai_config,
    tools=["code_complexity", "python_code_execution"],
    system_message="You are a code analysis expert."
)

await autogen_workbench.create_agent_with_model(
    agent_name="writer",
    model_config=anthropic_config,
    tools=["http_request"],
    system_message="You are a documentation specialist."
)

# Coordinate agents on a task
results = await autogen_workbench.coordinate_agents(
    task="Analyze code and create documentation",
    agent_names=["analyst", "writer"]
)
```

## Complete Example

```python
import asyncio
from agent_framework import AgentOrchestrator, FrameworkConfig
from agent_framework.core.config import (
    ModelConfig, ModelProvider, MultiAgentConfig, AgentRoleConfig
)

async def main():
    # Define model configurations
    configs = {
        "openai": ModelConfig(
            provider=ModelProvider.OPENAI,
            model="gpt-4o",
            api_key="your-openai-key",
            temperature=0.3
        ),
        "anthropic": ModelConfig(
            provider=ModelProvider.ANTHROPIC,
            model="claude-3-5-sonnet-20241022",
            api_key="your-anthropic-key",
            temperature=0.8
        ),
        "local": ModelConfig(
            provider=ModelProvider.OLLAMA,
            model="codellama:7b",
            base_url="http://localhost:11434",
            temperature=0.5
        )
    }
    
    # Create multi-agent configuration
    multi_agent_config = MultiAgentConfig(
        enabled=True,
        agent_roles={
            "analyst": AgentRoleConfig(
                name="analyst",
                description="Code analysis specialist",
                capabilities=["code_analysis"],
                model_config=configs["openai"]  # Uses OpenAI
            ),
            "writer": AgentRoleConfig(
                name="writer", 
                description="Documentation specialist",
                capabilities=["documentation"],
                model_config=configs["anthropic"]  # Uses Anthropic
            ),
            "tester": AgentRoleConfig(
                name="tester",
                description="Test generation specialist", 
                capabilities=["testing"],
                model_config=configs["local"]  # Uses local Ollama
            )
        }
    )
    
    # Create framework configuration
    framework_config = FrameworkConfig(
        multi_agent=multi_agent_config,
        model=configs["openai"]  # Global fallback
    )
    
    # Initialize and use the orchestrator
    orchestrator = AgentOrchestrator(framework_config)
    await orchestrator.initialize_multi_agent()
    
    # Execute tasks with different agents/models
    # ... task execution code ...
    
    await orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
```

## Error Handling and Recovery

The framework includes comprehensive error handling:

```python
from agent_framework.core.error_handling import error_handler

# Automatic error classification and recovery
try:
    client = model_client_factory.create_client(config)
except ModelClientError as e:
    print(f"Model client error: {e.message}")
    print(f"Category: {e.category}")
    print(f"Severity: {e.severity}")
    
    # Check error statistics
    stats = error_handler.get_error_statistics()
    print(f"Total errors: {stats['total_errors']}")
```

## Best Practices

### 1. Model Selection Guidelines

- **Code Analysis**: Use GPT-4 or Claude-3.5-sonnet for complex reasoning
- **Documentation**: Use Claude models for creative and comprehensive writing
- **Testing**: Use local models for privacy or cost-sensitive operations
- **Research**: Use models with web access capabilities

### 2. Fallback Strategy

Always configure fallback models:
```python
primary_config = ModelConfig(
    provider=ModelProvider.OPENAI,
    model="gpt-4o",
    fallback_configs=[fallback_config]
)
```

### 3. Resource Management

- Monitor token usage across different providers
- Use local models for high-volume, simple tasks
- Reserve premium models for complex reasoning

### 4. Security Considerations

- Store API keys in environment variables
- Use Azure/enterprise providers for sensitive data
- Consider local models for privacy-critical applications

## Migration from Single Model

To migrate existing single-model configurations:

1. **Keep existing global model** as fallback
2. **Add per-agent configurations** gradually
3. **Test with fallback mechanisms** enabled
4. **Monitor performance and costs**

```python
# Before (single model)
config = FrameworkConfig(
    model=ModelConfig(provider=ModelProvider.OPENAI, model="gpt-4")
)

# After (per-agent models with fallback)
config = FrameworkConfig(
    model=ModelConfig(provider=ModelProvider.OPENAI, model="gpt-4"),  # Fallback
    multi_agent=MultiAgentConfig(
        agent_roles={
            "specialist": AgentRoleConfig(
                name="specialist",
                model_config=specialized_config  # Per-agent model
            )
        }
    )
)
```

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure keys are set in environment variables
2. **Network Timeouts**: Configure appropriate timeout values
3. **Model Unavailability**: Use fallback configurations
4. **Resource Limits**: Monitor usage and implement rate limiting

### Debug Mode

Enable debug logging to troubleshoot issues:
```python
config = FrameworkConfig(debug=True)
```

This will provide detailed logs about model client initialization, fallback attempts, and error recovery.
