"""
Example demonstrating per-agent model configuration in the agent framework.

This example shows how to:
1. Configure different model providers for different agents
2. Use fallback configurations
3. Leverage AutoGen's tools and workbench patterns
4. Handle model client initialization and error scenarios
"""

import asyncio
import logging
from pathlib import Path

from agent_framework import AgentOrchestrator, FrameworkConfig
from agent_framework.core.config import (
    ModelConfig, ModelProvider, MultiAgentConfig, AgentRoleConfig, 
    MCPConfig, MCPServerConfig
)
from agent_framework.core.types import Task, TaskPriority
from agent_framework.core.multi_agent_types import AgentCapability


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_per_agent_model_config() -> FrameworkConfig:
    """Create a configuration with per-agent model settings."""
    
    # Global fallback model configuration (OpenRouter)
    global_model_config = ModelConfig(
        provider=ModelProvider.OPENROUTER,
        model="qwen/qwen3-coder:free",
        api_key="your-openrouter-api-key-here",  # Replace with your actual API key
        base_url="https://openrouter.ai/api/v1",
        temperature=0.7,
        max_tokens=4096
    )
    
    # OpenAI model configuration for code analysis
    openai_config = ModelConfig(
        provider=ModelProvider.OPENAI,
        model="gpt-4o",
        api_key="your-openai-api-key-here",  # Replace with your actual API key
        temperature=0.3,  # Lower temperature for more focused analysis
        max_tokens=8192,
        # Fallback to global config if OpenAI fails
        fallback_configs=[global_model_config]
    )
    
    # Anthropic model configuration for documentation
    anthropic_config = ModelConfig(
        provider=ModelProvider.ANTHROPIC,
        model="claude-3-5-sonnet-20241022",
        api_key="your-anthropic-api-key-here",  # Replace with your actual API key
        temperature=0.8,  # Higher temperature for creative documentation
        max_tokens=4096,
        # Fallback to global config if Anthropic fails
        fallback_configs=[global_model_config]
    )
    
    # Local Ollama configuration for testing
    ollama_config = ModelConfig(
        provider=ModelProvider.OLLAMA,
        model="codellama:7b",
        base_url="http://localhost:11434",
        temperature=0.5,
        max_tokens=2048,
        # Fallback to global config if Ollama is not available
        fallback_configs=[global_model_config]
    )
    
    # Azure OpenAI configuration for optimization
    azure_config = ModelConfig(
        provider=ModelProvider.AZURE_OPENAI,
        model="gpt-4",
        api_key="your-azure-api-key-here",  # Replace with your actual API key
        azure_endpoint="https://your-resource.openai.azure.com/",
        azure_deployment="gpt-4-deployment",
        azure_api_version="2024-02-01",
        temperature=0.4,
        max_tokens=4096,
        # Fallback to global config if Azure fails
        fallback_configs=[global_model_config]
    )
    
    # MCP server configuration for enhanced capabilities
    mcp_config = MCPConfig(
        servers={
            "filesystem": MCPServerConfig(
                command="npx",
                args=["-y", "@modelcontextprotocol/server-filesystem", str(Path.cwd())],
                description="Filesystem access for reading and writing files"
            ),
            "fetch": MCPServerConfig(
                command="uvx",
                args=["mcp-server-fetch"],
                description="Web content fetching capabilities"
            ),
            "brave_search": MCPServerConfig(
                command="npx",
                args=["-y", "@modelcontextprotocol/server-brave-search"],
                description="Web search capabilities via Brave Search API"
            )
        }
    )
    
    # Multi-agent configuration with per-agent model settings
    multi_agent_config = MultiAgentConfig(
        enabled=True,
        max_agents=6,
        coordination_strategy="capability_based",
        task_delegation_enabled=True,
        result_sharing_enabled=True,
        conflict_resolution_strategy="voting",
        agent_roles={
            "code_analyst": AgentRoleConfig(
                name="code_analyst",
                description="Specialized in deep code analysis using GPT-4",
                system_message="You are an expert code analyst. Use GPT-4's advanced reasoning to provide detailed code analysis, identify patterns, and suggest improvements.",
                capabilities=[AgentCapability.CODE_ANALYSIS.value, AgentCapability.ERROR_DETECTION.value],
                model_config=openai_config,  # Uses OpenAI GPT-4
                mcp_servers=["filesystem"],
                max_concurrent_tasks=3,
                priority=3
            ),
            "documenter": AgentRoleConfig(
                name="documenter",
                description="Specialized in creating comprehensive documentation using Claude",
                system_message="You are a documentation expert using Claude's excellent writing capabilities. Create clear, comprehensive, and well-structured documentation.",
                capabilities=[AgentCapability.DOCUMENTATION.value],
                model_config=anthropic_config,  # Uses Anthropic Claude
                mcp_servers=["filesystem", "fetch"],
                max_concurrent_tasks=2,
                priority=2
            ),
            "tester": AgentRoleConfig(
                name="tester",
                description="Specialized in test generation using local Ollama model",
                system_message="You are a testing expert using a local code model. Generate comprehensive test suites and validate code functionality.",
                capabilities=[AgentCapability.TESTING.value],
                model_config=ollama_config,  # Uses local Ollama
                mcp_servers=["filesystem"],
                max_concurrent_tasks=2,
                priority=2
            ),
            "optimizer": AgentRoleConfig(
                name="optimizer",
                description="Specialized in code optimization using Azure OpenAI",
                system_message="You are a performance optimization expert. Analyze code for performance bottlenecks and suggest optimizations.",
                capabilities=[AgentCapability.OPTIMIZATION.value],
                model_config=azure_config,  # Uses Azure OpenAI
                mcp_servers=["filesystem"],
                max_concurrent_tasks=2,
                priority=2
            ),
            "refactorer": AgentRoleConfig(
                name="refactorer",
                description="Specialized in refactoring using global fallback model",
                system_message="You are a refactoring expert. Improve code structure, readability, and maintainability.",
                capabilities=[AgentCapability.REFACTORING.value],
                # No model_config specified - will use global fallback
                mcp_servers=["filesystem"],
                max_concurrent_tasks=2,
                priority=1
            ),
            "web_researcher": AgentRoleConfig(
                name="web_researcher",
                description="Specialized in web research with search capabilities",
                system_message="You are a research expert. Use web search and content fetching to gather information and provide comprehensive research results.",
                capabilities=[AgentCapability.WEB_SEARCH.value],
                model_config=anthropic_config,  # Uses Anthropic for research
                mcp_servers=["fetch", "brave_search"],
                max_concurrent_tasks=3,
                priority=2
            )
        }
    )
    
    return FrameworkConfig(
        name="Per-Agent Model Configuration Demo",
        debug=True,
        model=global_model_config,  # Global fallback
        multi_agent=multi_agent_config,
        mcp=mcp_config
    )


async def demonstrate_per_agent_models():
    """Demonstrate per-agent model configuration."""
    print("🚀 Starting Per-Agent Model Configuration Demo")
    print("=" * 60)
    
    # Create configuration
    config = create_per_agent_model_config()
    
    # Initialize orchestrator
    orchestrator = AgentOrchestrator(config)
    
    try:
        # Initialize the multi-agent system
        print("📋 Initializing multi-agent system...")
        await orchestrator.initialize_multi_agent()
        
        # Display agent configurations
        print("\n🤖 Agent Model Configurations:")
        print("-" * 40)
        
        for role_name, role_config in config.multi_agent.agent_roles.items():
            if role_config.model_config:
                provider = role_config.model_config.provider
                model = role_config.model_config.model
                print(f"  {role_name}: {provider} - {model}")
            else:
                print(f"  {role_name}: Global fallback - {config.model.provider} - {config.model.model}")
        
        # Create sample tasks for different agents
        tasks = [
            Task(
                name="analyze_code_quality",
                description="Analyze the code quality of the agent framework",
                parameters={"file_path": "agent_framework/core/orchestrator.py"},
                priority=TaskPriority.HIGH
            ),
            Task(
                name="generate_documentation",
                description="Create comprehensive documentation for the model configuration system",
                parameters={"topic": "per-agent model configuration"},
                priority=TaskPriority.MEDIUM
            ),
            Task(
                name="create_tests",
                description="Generate unit tests for the model client factory",
                parameters={"module": "model_client_factory"},
                priority=TaskPriority.MEDIUM
            ),
            Task(
                name="optimize_performance",
                description="Analyze and suggest performance optimizations",
                parameters={"focus": "model client initialization"},
                priority=TaskPriority.LOW
            ),
            Task(
                name="research_best_practices",
                description="Research best practices for multi-model agent systems",
                parameters={"query": "multi-model agent architecture patterns"},
                priority=TaskPriority.MEDIUM
            )
        ]
        
        # Execute tasks using multi-agent coordination
        print("\n⚡ Executing tasks with different model configurations...")
        print("-" * 50)
        
        results = []
        for task in tasks:
            try:
                print(f"  📝 Executing: {task.name}")
                result = await orchestrator.execute_task(task)
                results.append(result)
                print(f"  ✅ Completed: {task.name} - Status: {result.status}")
            except Exception as e:
                print(f"  ❌ Failed: {task.name} - Error: {e}")
        
        # Display results summary
        print(f"\n📊 Execution Summary:")
        print(f"  Total tasks: {len(tasks)}")
        print(f"  Completed: {len([r for r in results if r.status.value == 'completed'])}")
        print(f"  Failed: {len(tasks) - len(results)}")
        
        # Demonstrate model switching and fallback
        print("\n🔄 Demonstrating model fallback scenarios...")
        print("-" * 45)
        
        # This would demonstrate what happens when a primary model fails
        # and the system falls back to the configured fallback models
        print("  Note: Fallback behavior is automatically handled by the ModelClientFactory")
        print("  when primary model providers are unavailable or return errors.")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        await orchestrator.shutdown()
        print("✅ Demo completed!")


async def main():
    """Main function."""
    setup_logging()
    
    print("Per-Agent Model Configuration Example")
    print("=====================================")
    print()
    print("This example demonstrates:")
    print("• Different model providers for different agents")
    print("• Fallback configuration mechanisms")
    print("• AutoGen integration patterns")
    print("• Multi-agent coordination with diverse models")
    print()
    
    await demonstrate_per_agent_models()


if __name__ == "__main__":
    asyncio.run(main())
