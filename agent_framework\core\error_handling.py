"""
Comprehensive error handling for the agent framework.

This module provides robust error handling for model client initialization,
configuration validation, and runtime scenarios in the multi-model agent system.
"""

import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Type, Union
from dataclasses import dataclass
from datetime import datetime

from .config import ModelConfig, ModelProvider


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    CONFIGURATION = "configuration"
    MODEL_CLIENT = "model_client"
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    RESOURCE = "resource"
    RUNTIME = "runtime"
    VALIDATION = "validation"


@dataclass
class ErrorContext:
    """Context information for errors."""
    timestamp: datetime
    agent_name: Optional[str] = None
    model_config: Optional[ModelConfig] = None
    operation: Optional[str] = None
    additional_info: Optional[Dict[str, Any]] = None


class AgentFrameworkError(Exception):
    """Base exception for agent framework errors."""
    
    def __init__(self, 
                 message: str,
                 category: ErrorCategory,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[ErrorContext] = None,
                 cause: Optional[Exception] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            category: Error category
            severity: Error severity
            context: Error context
            cause: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or ErrorContext(timestamp=datetime.now())
        self.cause = cause


class ModelClientError(AgentFrameworkError):
    """Error related to model client operations."""
    
    def __init__(self, message: str, model_config: Optional[ModelConfig] = None, cause: Optional[Exception] = None):
        context = ErrorContext(
            timestamp=datetime.now(),
            model_config=model_config,
            operation="model_client_operation"
        )
        super().__init__(message, ErrorCategory.MODEL_CLIENT, ErrorSeverity.HIGH, context, cause)


class ConfigurationError(AgentFrameworkError):
    """Error related to configuration validation."""
    
    def __init__(self, message: str, config_field: Optional[str] = None, cause: Optional[Exception] = None):
        context = ErrorContext(
            timestamp=datetime.now(),
            operation="configuration_validation",
            additional_info={"config_field": config_field} if config_field else None
        )
        super().__init__(message, ErrorCategory.CONFIGURATION, ErrorSeverity.MEDIUM, context, cause)


class NetworkError(AgentFrameworkError):
    """Error related to network operations."""
    
    def __init__(self, message: str, endpoint: Optional[str] = None, cause: Optional[Exception] = None):
        context = ErrorContext(
            timestamp=datetime.now(),
            operation="network_operation",
            additional_info={"endpoint": endpoint} if endpoint else None
        )
        super().__init__(message, ErrorCategory.NETWORK, ErrorSeverity.HIGH, context, cause)


class AuthenticationError(AgentFrameworkError):
    """Error related to authentication."""
    
    def __init__(self, message: str, provider: Optional[ModelProvider] = None, cause: Optional[Exception] = None):
        context = ErrorContext(
            timestamp=datetime.now(),
            operation="authentication",
            additional_info={"provider": provider.value if provider else None}
        )
        super().__init__(message, ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH, context, cause)


class ResourceError(AgentFrameworkError):
    """Error related to resource limitations."""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, cause: Optional[Exception] = None):
        context = ErrorContext(
            timestamp=datetime.now(),
            operation="resource_management",
            additional_info={"resource_type": resource_type} if resource_type else None
        )
        super().__init__(message, ErrorCategory.RESOURCE, ErrorSeverity.MEDIUM, context, cause)


class ErrorHandler:
    """
    Centralized error handler for the agent framework.
    
    Provides error classification, logging, recovery strategies,
    and error reporting capabilities.
    """
    
    def __init__(self):
        """Initialize the error handler."""
        self.logger = logging.getLogger(__name__)
        self._error_history: List[AgentFrameworkError] = []
        self._recovery_strategies: Dict[ErrorCategory, callable] = {}
        self._setup_default_recovery_strategies()
    
    def _setup_default_recovery_strategies(self) -> None:
        """Setup default recovery strategies for different error categories."""
        self._recovery_strategies.update({
            ErrorCategory.MODEL_CLIENT: self._recover_model_client_error,
            ErrorCategory.NETWORK: self._recover_network_error,
            ErrorCategory.AUTHENTICATION: self._recover_authentication_error,
            ErrorCategory.CONFIGURATION: self._recover_configuration_error,
            ErrorCategory.RESOURCE: self._recover_resource_error,
        })
    
    def handle_error(self, error: Exception, context: Optional[ErrorContext] = None) -> AgentFrameworkError:
        """
        Handle and classify an error.
        
        Args:
            error: The original error
            context: Error context
            
        Returns:
            Classified framework error
        """
        # Classify the error
        framework_error = self._classify_error(error, context)
        
        # Log the error
        self._log_error(framework_error)
        
        # Store in history
        self._error_history.append(framework_error)
        
        # Attempt recovery if strategy exists
        if framework_error.category in self._recovery_strategies:
            try:
                recovery_result = self._recovery_strategies[framework_error.category](framework_error)
                if recovery_result:
                    self.logger.info(f"Successfully recovered from {framework_error.category.value} error")
            except Exception as recovery_error:
                self.logger.error(f"Recovery failed for {framework_error.category.value}: {recovery_error}")
        
        return framework_error
    
    def _classify_error(self, error: Exception, context: Optional[ErrorContext] = None) -> AgentFrameworkError:
        """Classify an error into framework error categories."""
        error_message = str(error)
        error_type = type(error).__name__
        
        # Check if it's already a framework error
        if isinstance(error, AgentFrameworkError):
            return error
        
        # Classify based on error type and message
        if "api_key" in error_message.lower() or "authentication" in error_message.lower():
            return AuthenticationError(f"Authentication failed: {error_message}", cause=error)
        
        elif "connection" in error_message.lower() or "timeout" in error_message.lower():
            return NetworkError(f"Network error: {error_message}", cause=error)
        
        elif "config" in error_message.lower() or "validation" in error_message.lower():
            return ConfigurationError(f"Configuration error: {error_message}", cause=error)
        
        elif "memory" in error_message.lower() or "resource" in error_message.lower():
            return ResourceError(f"Resource error: {error_message}", cause=error)
        
        elif any(provider.value in error_message.lower() for provider in ModelProvider):
            return ModelClientError(f"Model client error: {error_message}", cause=error)
        
        else:
            # Default to runtime error
            return AgentFrameworkError(
                f"Runtime error ({error_type}): {error_message}",
                ErrorCategory.RUNTIME,
                ErrorSeverity.MEDIUM,
                context,
                error
            )
    
    def _log_error(self, error: AgentFrameworkError) -> None:
        """Log an error with appropriate level based on severity."""
        log_message = f"[{error.category.value.upper()}] {error.message}"
        
        if error.context:
            if error.context.agent_name:
                log_message += f" (Agent: {error.context.agent_name})"
            if error.context.operation:
                log_message += f" (Operation: {error.context.operation})"
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, exc_info=error.cause)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, exc_info=error.cause)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _recover_model_client_error(self, error: AgentFrameworkError) -> bool:
        """Attempt to recover from model client errors."""
        if error.context and error.context.model_config:
            model_config = error.context.model_config
            if model_config.fallback_configs:
                self.logger.info(f"Attempting fallback for {model_config.provider}")
                return True
        return False
    
    def _recover_network_error(self, error: AgentFrameworkError) -> bool:
        """Attempt to recover from network errors."""
        # Could implement retry logic, alternative endpoints, etc.
        self.logger.info("Network error recovery not implemented")
        return False
    
    def _recover_authentication_error(self, error: AgentFrameworkError) -> bool:
        """Attempt to recover from authentication errors."""
        # Could implement token refresh, alternative auth methods, etc.
        self.logger.info("Authentication error recovery not implemented")
        return False
    
    def _recover_configuration_error(self, error: AgentFrameworkError) -> bool:
        """Attempt to recover from configuration errors."""
        # Could implement default value substitution, config repair, etc.
        self.logger.info("Configuration error recovery not implemented")
        return False
    
    def _recover_resource_error(self, error: AgentFrameworkError) -> bool:
        """Attempt to recover from resource errors."""
        # Could implement resource cleanup, alternative resources, etc.
        self.logger.info("Resource error recovery not implemented")
        return False
    
    def get_error_history(self, category: Optional[ErrorCategory] = None) -> List[AgentFrameworkError]:
        """Get error history, optionally filtered by category."""
        if category:
            return [error for error in self._error_history if error.category == category]
        return self._error_history.copy()
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics."""
        total_errors = len(self._error_history)
        if total_errors == 0:
            return {"total_errors": 0}
        
        category_counts = {}
        severity_counts = {}
        
        for error in self._error_history:
            category_counts[error.category.value] = category_counts.get(error.category.value, 0) + 1
            severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
        
        return {
            "total_errors": total_errors,
            "by_category": category_counts,
            "by_severity": severity_counts,
            "most_common_category": max(category_counts, key=category_counts.get),
            "most_common_severity": max(severity_counts, key=severity_counts.get)
        }
    
    def clear_error_history(self) -> None:
        """Clear the error history."""
        self._error_history.clear()
        self.logger.info("Error history cleared")


# Global error handler instance
error_handler = ErrorHandler()
